/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Offers Banner */
.offers-banner {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
    padding: 8px 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    max-width: 100vw;
    z-index: 1001;
    animation: bannerBlink 2s infinite alternate;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.offers-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.offer-text {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
    font-size: 14px;
    font-weight: 500;
}

.offer-highlight {
    font-weight: 700;
    font-size: 16px;
    animation: textBlink 1.5s infinite;
}

.offer-details {
    font-weight: 600;
}

.offer-cta {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
    animation: ctaBlink 2s infinite;
}

.offers-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 20px;
    font-weight: bold;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.offers-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

@keyframes bannerBlink {
    0% { background: linear-gradient(135deg, #ff6b35, #f7931e); }
    50% { background: linear-gradient(135deg, #f7931e, #ff6b35); }
    100% { background: linear-gradient(135deg, #ff6b35, #f7931e); }
}

@keyframes textBlink {
    0%, 50% { opacity: 1; }
    25%, 75% { opacity: 0.7; }
}

@keyframes ctaBlink {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Mobile styles for offers banner */
@media (max-width: 767px) {
    .offers-banner {
        padding: 6px 0;
    }

    .offers-content {
        padding: 0 15px;
    }

    .offer-text {
        gap: 8px;
        font-size: 12px;
    }

    .offer-highlight {
        font-size: 14px;
    }

    .offer-details {
        display: none; /* Hide on very small screens */
    }

    .offer-cta {
        padding: 3px 8px;
        font-size: 11px;
    }

    .offers-close {
        width: 25px;
        height: 25px;
        font-size: 16px;
        min-height: 44px; /* Touch-friendly */
        min-width: 44px;
    }

    .header {
        top: 38px; /* Adjusted for smaller mobile banner */
    }

    .hero {
        padding-top: 108px; /* Adjusted for mobile banner + header */
    }

    /* Improve mobile navigation */
    .nav-menu a {
        padding: 1rem 1.5rem !important;
        font-size: 1.1rem !important;
        min-height: 48px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Compact mobile header actions */
    .header-actions {
        display: flex !important;
        gap: 0.5rem !important;
        align-items: center !important;
    }

    .header-phone {
        display: none !important; /* Hide phone text on mobile */
    }

    .mobile-call-btn {
        display: flex !important;
        min-width: 44px !important;
        min-height: 44px !important;
        width: 44px !important;
        height: 44px !important;
    }

    .header-social {
        display: flex !important;
        gap: 0.3rem !important;
    }

    .social-icon {
        width: 36px !important;
        height: 36px !important;
        font-size: 0.9rem !important;
        min-width: 44px !important;
        min-height: 44px !important;
    }

    /* Adjust logo for mobile */
    .logo-icon {
        width: 35px !important;
        height: 35px !important;
        font-size: 1rem !important;
    }

    .brand-tagline {
        display: none !important;
    }

    .brand-text,
    .brand-highlight {
        font-size: 1.4rem !important;
    }

    /* Better mobile section spacing */
    .services,
    .portfolio,
    .testimonials,
    .contact {
        padding: 3rem 0 !important;
    }

    .section-header {
        margin-bottom: 2rem !important;
    }

    .section-header h2 {
        font-size: 1.8rem !important;
        line-height: 1.3 !important;
        margin-bottom: 0.8rem !important;
    }

    .section-header p {
        font-size: 1rem !important;
        line-height: 1.5 !important;
    }

    /* Mobile hero visual adjustments */
    .hero-stats {
        grid-template-columns: 1fr !important;
        gap: 1.2rem !important;
        max-width: 300px !important;
    }

    .stat-item {
        padding: 1.5rem 1rem !important;
        border-radius: 16px !important;
    }

    .stat-number {
        font-size: 1.8rem !important;
    }

    .stat-label {
        font-size: 0.85rem !important;
    }

    .hero-features {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
        max-width: 300px !important;
    }

    .feature-item {
        padding: 1.1rem 1rem !important;
        font-size: 0.9rem !important;
        border-radius: 14px !important;
    }

    .feature-item i {
        font-size: 1.2rem !important;
    }
}

@media (min-width: 481px) and (max-width: 767px) {
    .offer-details {
        display: inline; /* Show on larger mobile screens */
        font-size: 11px;
    }
}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
    width: 100%;
}

:root {
    --primary-color: #ff6b35;
    --secondary-color: #f7931e;
    --accent-color: #1e3a8a;
    --success-color: #10b981;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --white: #ffffff;
    --light-bg: #f9fafb;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Noto Sans Devanagari', 'Hind', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw;
    position: relative;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
    box-sizing: border-box;
}

/* Header Styles */
.header {
    position: fixed;
    top: 46px; /* Adjusted for offers banner */
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.header.banner-hidden {
    top: 0;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
}

/* Logo Styles */
.logo {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    position: relative;
}

.logo-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-3px); }
}

.brand-tagline {
    position: absolute;
    top: 100%;
    left: 45px;
    font-size: 0.7rem;
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.8;
}

/* Brand Name Styles */
.brand-name {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 0;
    gap: 0;
}

.brand-text {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
}

.brand-highlight {
    color: var(--secondary-color);
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    margin-left: 0.5rem;
    position: relative;
    z-index: 2;
}



.brand-name::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
    border-radius: 10px;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.brand-name:hover::before {
    opacity: 1;
}

.logo h2 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 700; /* Made bold */
    transition: var(--transition);
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    background: transparent;
}

.nav-link:hover {
    color: var(--primary-color);
    background: rgba(255, 107, 53, 0.1);
    transform: translateY(-2px);
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 700; /* Made bold */
    transition: var(--transition);
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.nav-menu a:hover::after {
    width: 100%;
}

/* Navigation Icons */
.nav-icon {
    margin-right: 0.5rem;
    font-size: 0.9rem;
    opacity: 0.8;
    transition: var(--transition);
}

.nav-link:hover .nav-icon {
    opacity: 1;
    transform: scale(1.1);
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-phone {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border: 2px solid var(--primary-color);
    border-radius: 25px;
    transition: var(--transition);
    background: transparent;
}

.header-phone:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
}

.header-phone i {
    animation: phoneRing 2s infinite;
}

@keyframes phoneRing {
    0%, 100% { transform: rotate(0deg); }
    10%, 30% { transform: rotate(-10deg); }
    20% { transform: rotate(10deg); }
}

.header-social {
    display: flex;
    gap: 0.5rem;
}

.social-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition);
    font-size: 1rem;
}

.social-icon.whatsapp {
    background: #25d366;
    color: white;
}

.social-icon.facebook {
    background: #1877f2;
    color: white;
}

.social-icon:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Mobile Call Button */
.mobile-call-btn {
    display: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), #e64a19);
    color: white;
    text-decoration: none;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
    transition: all 0.3s ease;
    animation: mobilePulse 2s infinite;
    position: relative;
    z-index: 10;
}

@keyframes mobilePulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(255, 107, 53, 0.6);
    }
}

.mobile-call-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.6);
}

/* Header Decoration */
.header-decoration {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--primary-color));
    background-size: 200% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.decoration-dots {
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    animation: dotPulse 2s infinite;
}

.dot:nth-child(2) {
    animation-delay: 0.3s;
    background: var(--secondary-color);
}

.dot:nth-child(3) {
    animation-delay: 0.6s;
}

@keyframes dotPulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.3); opacity: 1; }
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: var(--white);
    padding-top: 126px; /* Adjusted for offers banner + header */
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typing Animation Styles Removed */

.hero-subtitle {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.9);
    opacity: 0;
    animation: fadeInUp 1s ease-out 3s forwards;
}

.hero-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0;
    animation: fadeInUp 1s ease-out 4s forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-content h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--transition);
    cursor: pointer;
}

.btn-primary {
    background: var(--white);
    color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--light-bg);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-secondary:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* Hero Visual Elements */
.hero-visual {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    align-items: center;
    justify-content: center;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    width: 100%;
    max-width: 400px;
}

.stat-item {
    text-align: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
    padding: 1.8rem 1.2rem;
    border-radius: 20px;
    backdrop-filter: blur(15px);
    border: 1.5px solid rgba(255, 255, 255, 0.25);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    animation: statFloat 3s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-item:hover::before {
    opacity: 1;
}

.stat-item:nth-child(1) {
    animation-delay: 0s;
}

.stat-item:nth-child(2) {
    animation-delay: 0.5s;
}

.stat-item:nth-child(3) {
    animation-delay: 1s;
}

@keyframes statFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-8px) rotate(1deg); }
}

.stat-item:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
    transform: translateY(-5px) scale(1.03);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.stat-number {
    font-size: 2.2rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #ffffff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.95);
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.hero-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.2rem;
    width: 100%;
    max-width: 380px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
    padding: 1.3rem 1.1rem;
    border-radius: 16px;
    backdrop-filter: blur(15px);
    border: 1.5px solid rgba(255, 255, 255, 0.25);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.95);
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-item:hover::before {
    opacity: 1;
}

.feature-item:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
    transform: translateY(-5px) scale(1.03);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.feature-item i {
    font-size: 1.4rem;
    color: var(--secondary-color);
    background: linear-gradient(45deg, var(--secondary-color), #ffd700);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: all 0.3s ease;
    z-index: 2;
    position: relative;
}

.feature-item:hover i {
    transform: scale(1.2) rotate(5deg);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.feature-item span {
    z-index: 2;
    position: relative;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Marquee Section */
.marquee-section {
    background: linear-gradient(135deg, var(--accent-color), #1e40af);
    color: var(--white);
    padding: 1rem 0;
    overflow: hidden;
    position: relative;
}

.marquee-container {
    width: 100%;
    overflow: hidden;
}

.marquee-content {
    display: flex;
    animation: marquee 30s linear infinite;
    white-space: nowrap;
}

.marquee-content span {
    padding: 0 3rem;
    font-size: 1.1rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    min-width: max-content;
}

@keyframes marquee {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}

.marquee-section:hover .marquee-content {
    animation-play-state: paused;
}

/* Services Section */
.services {
    padding: 5rem 0;
    background: var(--light-bg);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.2rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(350px, 100%), 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
    border: 1px solid transparent;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--white);
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.service-card p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.service-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: var(--transition);
    margin-top: 1rem;
}

.service-link:hover {
    color: var(--secondary-color);
    transform: translateX(5px);
}

.service-link::after {
    content: '→';
    transition: var(--transition);
}

.service-link:hover::after {
    transform: translateX(3px);
}

/* Quick Actions */
.quick-actions {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 1rem;
    z-index: 999;
}

.quick-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: var(--white);
    font-size: 1.5rem;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
}

.call-btn {
    background: var(--accent-color);
}

.whatsapp-btn {
    background: #25d366;
}

.website-btn {
    background: var(--primary-color);
}

.app-btn {
    background: var(--secondary-color);
}

.quick-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 15px 25px -5px rgba(0, 0, 0, 0.2);
}

/* Portfolio Section */
.portfolio {
    padding: 5rem 0;
    background: var(--white);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
    gap: 2rem;
}

.portfolio-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.portfolio-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.portfolio-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--white);
    padding: 2rem;
    transform: translateY(100%);
    transition: var(--transition);
}

.portfolio-item:hover .portfolio-overlay {
    transform: translateY(0);
}

.portfolio-item:hover img {
    transform: scale(1.1);
}

.portfolio-overlay h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Testimonials Section */
.testimonials {
    padding: 5rem 0;
    background: var(--light-bg);
}

/* Testimonial Stats */
.testimonial-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
    margin-bottom: 3rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(350px, 100%), 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 280px;
}

.testimonial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.testimonial-card.featured {
    border: 2px solid var(--primary-color);
    transform: scale(1.02);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.testimonial-card.featured:hover {
    transform: translateY(-5px) scale(1.02);
}

/* Testimonial Rating */
.testimonial-rating {
    display: flex;
    gap: 0.2rem;
    margin-bottom: 1rem;
}

.testimonial-rating i {
    color: #ffd700;
    font-size: 1rem;
}

.testimonial-content {
    flex: 1;
    margin-bottom: 1rem;
}

.testimonial-content p {
    font-style: italic;
    color: var(--text-light);
    line-height: 1.6;
    position: relative;
    padding-left: 1rem;
    margin: 0;
    font-size: 0.95rem;
}

.testimonial-content p::before {
    content: '"';
    font-size: 2rem;
    color: var(--primary-color);
    position: absolute;
    top: -5px;
    left: -5px;
    line-height: 1;
}

.testimonial-author {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-top: 1rem;
}

.testimonial-author img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--primary-color);
    flex-shrink: 0;
}

.author-info {
    flex: 1;
    min-width: 0;
}

.author-info h4 {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.3rem;
    font-size: 1rem;
    line-height: 1.2;
}

.author-info span {
    color: var(--text-light);
    font-size: 0.85rem;
    display: block;
    margin-bottom: 0.3rem;
    line-height: 1.3;
}

.author-location {
    font-size: 0.75rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 0.3rem;
    line-height: 1.2;
}

.author-location i {
    font-size: 0.7rem;
    flex-shrink: 0;
}

/* FAQ Section */
.faq {
    padding: 5rem 0;
    background: var(--white);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--white);
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: var(--transition);
}

.faq-item:hover {
    box-shadow: var(--shadow);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--light-bg);
    transition: var(--transition);
}

.faq-question:hover {
    background: #f3f4f6;
}

.faq-question h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
}

.faq-question i {
    color: var(--primary-color);
    transition: var(--transition);
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 1.5rem;
    max-height: 200px;
}

.faq-answer p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background: var(--light-bg);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.contact-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.contact-item i {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
}

.contact-item h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.contact-item p {
    color: var(--text-light);
}

.contact-form {
    background: var(--white);
    padding: 2.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

/* Mobile contact improvements */
@media (max-width: 767px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-info {
        order: 2;
    }

    .contact-form {
        order: 1;
        margin: 0;
    }

    .contact-item {
        padding: 1rem;
    }

    .contact-item i {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Mobile-friendly footer improvements */
@media (max-width: 767px) {
    .footer-section ul li a {
        padding: 0.4rem 0;
        min-height: 44px;
        display: flex;
        align-items: center;
        border-radius: 4px;
        transition: var(--transition);
    }

    .footer-section ul li a:hover {
        background: rgba(255, 107, 53, 0.1);
        padding-left: 0.5rem;
    }

    .social-links a {
        min-height: 44px;
        min-width: 44px;
    }

    .footer-section .contact-info p {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.3rem 0;
        min-height: 44px;
    }

    .footer-section .contact-info i {
        flex-shrink: 0;
    }
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.footer-section p {
    color: #d1d5db;
    line-height: 1.6;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #d1d5db;
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.9rem;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1rem;
    text-align: center;
    color: #9ca3af;
    font-size: 0.85rem;
}

/* WhatsApp Float Button */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 60px;
    height: 60px;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.8rem;
    text-decoration: none;
    box-shadow: var(--shadow-lg);
    z-index: 999;
    transition: var(--transition);
    animation: pulse 2s infinite;
}

.whatsapp-float:hover {
    transform: scale(1.1);
    background: #128c7e;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* Get Started Fixed Button */
.get-started-fixed {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 999;
}

.get-started-fixed .btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    box-shadow: var(--shadow-lg);
    animation: bounce 2s infinite;
}

.get-started-fixed .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 25px -5px rgba(255, 107, 53, 0.4);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Responsive Design */

/* Large Desktop Screens (1440px and above) */
@media (min-width: 1440px) {
    .container {
        max-width: 1400px;
    }

    .hero-content h1 {
        font-size: 3.5rem;
    }

    .hero-content h2 {
        font-size: 2.5rem;
    }

    .section-header h2 {
        font-size: 3rem;
    }

    .service-card {
        padding: 3rem;
    }

    .service-icon {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }
}

/* Desktop and Large Tablets (1024px to 1439px) */
@media (min-width: 1024px) and (max-width: 1439px) {
    .container {
        max-width: 1200px;
    }

    .hero-container {
        gap: 3rem;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .portfolio-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .testimonial-stats {
        gap: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

/* Tablets (768px to 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .container {
        padding: 0 30px;
    }

    .hero-container {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content h2 {
        font-size: 1.8rem;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .service-card {
        padding: 2rem;
    }

    .portfolio-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .testimonial-stats {
        flex-direction: column;
        gap: 1.5rem;
    }

    .stat-item {
        padding: 0.5rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .testimonial-card {
        padding: 1rem;
        min-height: auto;
    }

    .testimonial-author {
        gap: 0.8rem;
        margin-top: 0.8rem;
    }

    .testimonial-author img {
        width: 40px;
        height: 40px;
    }

    .author-info h4 {
        font-size: 0.9rem;
    }

    .author-info span {
        font-size: 0.8rem;
    }

    .author-location {
        font-size: 0.7rem;
    }

    .contact-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .quick-actions {
        right: 15px;
    }

    .quick-btn {
        width: 55px;
        height: 55px;
        font-size: 1.3rem;
    }

    .marquee-content span {
        font-size: 1.05rem;
        padding: 0 2.5rem;
    }
}

/* Mobile Devices (up to 767px) */
@media (max-width: 767px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
        z-index: 999;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .brand-text,
    .brand-highlight {
        font-size: 1.5rem;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
        padding: 2rem 20px;
    }

    .hero-content h1 {
        font-size: 2rem;
        line-height: 1.3;
    }

    .hero-content h2,
    .hero-subtitle {
        font-size: 1.5rem;
        line-height: 1.4;
    }

    .hero-content p,
    .hero-description {
        font-size: 1.1rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-card {
        padding: 2rem 1.5rem;
    }

    .service-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .quick-actions {
        right: 10px;
        gap: 0.5rem;
    }

    .quick-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .get-started-fixed {
        bottom: 10px;
        right: 10px;
    }

    .whatsapp-float {
        bottom: 10px;
        left: 10px;
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .section-header p {
        font-size: 1.1rem;
    }

    .marquee-content span {
        font-size: 0.95rem;
        padding: 0 1.5rem;
    }
}

/* Large Mobile Devices (481px to 767px) */
@media (min-width: 481px) and (max-width: 767px) {
    .hero-buttons {
        flex-direction: row;
        justify-content: center;
        gap: 1rem;
    }

    .hero-buttons .btn {
        flex: 1;
        max-width: 200px;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .contact-form {
        padding: 2rem;
    }

    .popup-content,
    .announcement-content {
        width: 85%;
    }
}

/* Standard Mobile Devices (376px to 480px) */
@media (min-width: 376px) and (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-content h1 {
        font-size: 1.8rem;
        line-height: 1.2;
    }

    .hero-content h2 {
        font-size: 1.4rem;
    }

    .section-header h2 {
        font-size: 1.8rem;
    }

    .service-card,
    .testimonial-card {
        padding: 1.5rem;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .btn {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 280px;
    }

    .marquee-content span {
        font-size: 0.9rem;
        padding: 0 1rem;
    }

    .offer-item {
        padding: 1rem;
    }

    .popup-header,
    .announcement-header {
        padding: 1.5rem;
    }

    .popup-header h2,
    .announcement-header h2 {
        font-size: 1.5rem;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.loading {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    font-family: 'Noto Sans Devanagari', sans-serif;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    color: var(--white);
}

.notification-success .notification-content {
    background: var(--success-color);
}

.notification-error .notification-content {
    background: #ef4444;
}

.notification-info .notification-content {
    background: #3b82f6;
}

.notification-close {
    background: none;
    border: none;
    color: var(--white);
    font-size: 1.5rem;
    cursor: pointer;
    margin-left: auto;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.notification-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Lazy Loading Images */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* Print Styles */
@media print {
    .header,
    .quick-actions,
    .whatsapp-float,
    .get-started-fixed {
        display: none !important;
    }

    .hero {
        min-height: auto;
        padding: 2rem 0;
    }

    .section {
        page-break-inside: avoid;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #d97706;
        --secondary-color: #92400e;
        --text-dark: #000000;
        --text-light: #374151;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Focus Styles for Accessibility */
button:focus,
input:focus,
textarea:focus,
select:focus,
a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Skip to Content Link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10001;
}

.skip-link:focus {
    top: 6px;
}

/* Additional Mobile Improvements */
@media (max-width: 768px) {
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .services-grid {
        gap: 1.5rem;
    }

    .service-card {
        padding: 2rem 1.5rem;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .footer {
        padding: 2rem 0 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-section {
        padding: 0 1rem;
    }

    .footer-section h3,
    .footer-section h4 {
        font-size: 1.1rem;
        margin-bottom: 0.8rem;
    }

    .footer-section p {
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .footer-section ul li a {
        font-size: 0.85rem;
        padding: 0.2rem 0;
        display: block;
    }

    .social-links {
        justify-content: center;
        gap: 0.8rem;
        margin-top: 1.5rem;
    }

    .social-links a {
        width: 35px;
        height: 35px;
    }

    .footer-bottom {
        font-size: 0.8rem;
        padding-top: 1.5rem;
        margin-top: 1.5rem;
    }
}

/* Small Mobile Devices (321px to 375px) */
@media (min-width: 321px) and (max-width: 375px) {
    .container {
        padding: 0 12px;
    }

    .hero-content h1 {
        font-size: 1.6rem;
        line-height: 1.2;
    }

    .hero-content h2 {
        font-size: 1.3rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .section-header h2 {
        font-size: 1.7rem;
    }

    .service-card,
    .testimonial-card {
        padding: 1.2rem;
    }

    .service-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .btn {
        padding: 0.75rem 1.2rem;
        font-size: 0.95rem;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 250px;
    }

    .marquee-content span {
        font-size: 0.85rem;
        padding: 0 0.8rem;
    }

    .quick-btn {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .whatsapp-float {
        width: 45px;
        height: 45px;
        font-size: 1.3rem;
    }
}

/* Extra Small Devices (up to 320px) */
@media (max-width: 320px) {
    .container {
        padding: 0 10px;
    }

    .hero {
        padding-top: 70px;
        min-height: 90vh;
    }

    .hero-container {
        padding: 1rem 10px;
        gap: 1.5rem;
    }

    .hero-content h1 {
        font-size: 1.4rem;
        line-height: 1.2;
        margin-bottom: 0.8rem;
    }

    .hero-content h2 {
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .hero-content p {
        font-size: 0.95rem;
        margin-bottom: 1.5rem;
    }

    .section-header h2 {
        font-size: 1.5rem;
    }

    .section-header p {
        font-size: 1rem;
    }

    .service-card,
    .testimonial-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .service-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .service-card h3 {
        font-size: 1.2rem;
        margin-bottom: 0.8rem;
    }

    .btn {
        padding: 0.7rem 1rem;
        font-size: 0.9rem;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 0.8rem;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 220px;
    }

    .marquee-content span {
        font-size: 0.8rem;
        padding: 0 0.5rem;
    }

    .quick-actions {
        right: 5px;
        gap: 0.3rem;
    }

    .quick-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .whatsapp-float {
        bottom: 5px;
        left: 5px;
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .get-started-fixed {
        bottom: 5px;
        right: 5px;
    }

    .get-started-fixed .btn {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
    }

    .contact-form {
        padding: 1rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.8rem;
        font-size: 0.9rem;
    }

    .popup-content,
    .announcement-content {
        width: 95%;
        margin: 0.5rem;
    }

    .popup-header,
    .announcement-header {
        padding: 1rem;
    }

    .popup-header h2,
    .announcement-header h2 {
        font-size: 1.3rem;
    }

    .popup-body,
    .announcement-body {
        padding: 1rem;
    }

    .offer-item {
        flex-direction: column;
        text-align: center;
        padding: 0.8rem;
    }

    .offer-icon {
        font-size: 2rem;
        width: 50px;
        height: 50px;
    }

    .footer {
        padding: 1.5rem 0 0.8rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.2rem;
        text-align: center;
    }

    .footer-section {
        padding: 0 0.8rem;
    }

    .footer-section h3,
    .footer-section h4 {
        font-size: 1rem;
        margin-bottom: 0.6rem;
    }

    .footer-section p {
        font-size: 0.85rem;
        line-height: 1.4;
        margin-bottom: 0.8rem;
    }

    .footer-section ul li {
        margin-bottom: 0.3rem;
    }

    .footer-section ul li a {
        font-size: 0.8rem;
        padding: 0.15rem 0;
        display: block;
    }

    .social-links {
        justify-content: center;
        gap: 0.6rem;
        margin-top: 1rem;
    }

    .social-links a {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }

    .footer-bottom {
        font-size: 0.75rem;
        padding-top: 1rem;
        margin-top: 1rem;
        line-height: 1.3;
    }
}

/* Ultra Small Devices (up to 280px) */
@media (max-width: 280px) {
    .footer {
        padding: 1rem 0 0.5rem;
    }

    .footer-content {
        gap: 1rem;
    }

    .footer-section {
        padding: 0 0.5rem;
    }

    .footer-section h3,
    .footer-section h4 {
        font-size: 0.95rem;
        margin-bottom: 0.5rem;
    }

    .footer-section p {
        font-size: 0.8rem;
        line-height: 1.3;
        margin-bottom: 0.6rem;
    }

    .footer-section ul li {
        margin-bottom: 0.2rem;
    }

    .footer-section ul li a {
        font-size: 0.75rem;
        padding: 0.1rem 0;
    }

    .social-links {
        gap: 0.4rem;
        margin-top: 0.8rem;
    }

    .social-links a {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .footer-bottom {
        font-size: 0.7rem;
        padding-top: 0.8rem;
        margin-top: 0.8rem;
        line-height: 1.2;
    }
}

/* Offers Popup Styles */
.offers-popup,
.announcement-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.offers-popup.show,
.announcement-popup.show {
    opacity: 1;
    visibility: visible;
}

.popup-content,
.announcement-content {
    background: var(--white);
    border-radius: var(--border-radius);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    transform: scale(0.7);
    transition: transform 0.3s ease;
}

.offers-popup.show .popup-content,
.announcement-popup.show .announcement-content {
    transform: scale(1);
}

.popup-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    font-size: 2rem;
    color: var(--text-light);
    cursor: pointer;
    z-index: 1;
    transition: var(--transition);
}

.popup-close:hover {
    color: var(--primary-color);
    transform: scale(1.1);
}

.popup-header,
.announcement-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 2rem;
    text-align: center;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.popup-header h2,
.announcement-header h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.8rem;
}

.popup-header p {
    margin: 0;
    opacity: 0.9;
}

.popup-body,
.announcement-body {
    padding: 2rem;
}

.offer-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border: 2px solid #f3f4f6;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    transition: var(--transition);
}

.offer-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

.offer-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    color: var(--white);
}

.offer-details h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-dark);
    font-size: 1.2rem;
}

.original-price {
    text-decoration: line-through;
    color: var(--text-light);
    margin: 0;
    font-size: 0.9rem;
}

.offer-price {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0.25rem 0;
}

.offer-desc {
    color: var(--text-light);
    margin: 0;
    font-size: 0.9rem;
}

.popup-footer {
    background: var(--light-bg);
    padding: 1.5rem 2rem;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    text-align: center;
}

.offer-timer {
    color: var(--primary-color);
    font-weight: 600;
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
}

.popup-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.announcement-body p {
    margin: 1rem 0;
    padding: 0.75rem;
    background: var(--light-bg);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.announcement-footer {
    padding: 1.5rem 2rem;
    text-align: center;
    background: var(--light-bg);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Popup Responsive */
@media (max-width: 768px) {
    .popup-content,
    .announcement-content {
        width: 95%;
        margin: 1rem;
    }

    .popup-header,
    .announcement-header,
    .popup-body,
    .announcement-body {
        padding: 1.5rem;
    }

    .popup-footer,
    .announcement-footer {
        padding: 1rem 1.5rem;
    }

    .offer-item {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .popup-buttons {
        flex-direction: column;
    }

    .popup-buttons .btn {
        width: 100%;
    }

    .marquee-content span {
        font-size: 1rem;
        padding: 0 2rem;
    }
}

/* Landscape Orientation for Mobile Devices */
@media (max-width: 767px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
        padding-top: 60px;
    }

    .hero-container {
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
        align-items: center;
    }

    .hero-content h1 {
        font-size: 1.8rem;
        line-height: 1.1;
        margin-bottom: 0.5rem;
    }

    .hero-content h2 {
        font-size: 1.3rem;
        margin-bottom: 0.8rem;
    }

    .hero-content p {
        font-size: 0.95rem;
        margin-bottom: 1rem;
    }

    .hero-buttons {
        flex-direction: row;
        gap: 0.8rem;
    }

    .hero-buttons .btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }

    .services {
        padding: 3rem 0;
    }

    .section-header {
        margin-bottom: 2rem;
    }

    .section-header h2 {
        font-size: 1.8rem;
    }

    .quick-actions {
        top: 40%;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .btn,
    .quick-btn,
    .whatsapp-float {
        min-height: 44px;
        min-width: 44px;
    }

    .nav-menu a {
        padding: 1rem;
        font-size: 1.1rem;
    }

    .service-card:hover,
    .testimonial-card:hover,
    .portfolio-item:hover {
        transform: none;
    }

    .service-card:active,
    .testimonial-card:active,
    .portfolio-item:active {
        transform: scale(0.98);
    }

    .btn:active {
        transform: scale(0.95);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .portfolio-item img,
    .testimonial-author img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Foldable Devices */
@media (max-width: 280px) {
    .container {
        padding: 0 8px;
    }

    .hero-content h1 {
        font-size: 1.2rem;
    }

    .hero-content h2 {
        font-size: 1rem;
    }

    .btn {
        padding: 0.6rem 0.8rem;
        font-size: 0.8rem;
    }

    .service-card {
        padding: 0.8rem;
    }

    .service-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    .marquee-content span {
        font-size: 0.7rem;
        padding: 0 0.3rem;
    }

    .quick-btn {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .whatsapp-float {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

/* Large Screens (4K and above) */
@media (min-width: 1920px) {
    .container {
        max-width: 1600px;
    }

    .hero-content h1 {
        font-size: 4rem;
    }

    .hero-content h2 {
        font-size: 3rem;
    }

    .section-header h2 {
        font-size: 3.5rem;
    }

    .service-card {
        padding: 3.5rem;
    }

    .service-icon {
        width: 120px;
        height: 120px;
        font-size: 3rem;
    }

    .btn {
        padding: 1.2rem 2.5rem;
        font-size: 1.2rem;
    }

    .marquee-content span {
        font-size: 1.3rem;
        padding: 0 4rem;
    }
}

/* Mobile Viewport Fixes */
@media (max-width: 767px) {
    /* Prevent horizontal scrolling */
    * {
        max-width: 100vw;
        box-sizing: border-box;
    }

    /* Fix container widths */
    .container {
        padding: 0 15px;
        width: 100%;
        max-width: 100vw;
    }

    /* Ensure all sections fit properly */
    section {
        width: 100%;
        max-width: 100vw;
        overflow-x: hidden;
    }

    /* Fix hero section */
    .hero {
        width: 100%;
        max-width: 100vw;
        padding: 80px 0 60px;
    }

    /* Fix header */
    .header {
        width: 100%;
        max-width: 100vw;
    }

    /* Ensure hamburger menu is always visible on mobile */
    .hamburger {
        display: flex !important;
        z-index: 1001;
    }

    /* Fix navigation menu positioning */
    .nav-menu {
        width: 100vw;
        left: -100vw;
        max-width: 100vw;
    }

    .nav-menu.active {
        left: 0;
    }

    /* Fix text wrapping */
    h1, h2, h3, h4, h5, h6, p {
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
    }

    /* Fix images */
    img {
        max-width: 100%;
        height: auto;
    }

    /* Improve mobile text readability */
    .hero-content h1 {
        font-size: 1.8rem !important;
        line-height: 1.3 !important;
        margin-bottom: 1rem !important;
    }

    .hero-content h2 {
        font-size: 1.4rem !important;
        line-height: 1.4 !important;
        margin-bottom: 1rem !important;
    }

    .hero-content p {
        font-size: 1rem !important;
        line-height: 1.6 !important;
        margin-bottom: 1.5rem !important;
    }

    /* Fix button spacing on mobile */
    .hero-buttons {
        gap: 1rem !important;
        margin-top: 1.5rem !important;
    }

    .hero-buttons .btn {
        padding: 0.8rem 1.5rem !important;
        font-size: 0.95rem !important;
        min-height: 48px !important; /* Touch-friendly size */
    }

    /* Improve service cards on mobile */
    .service-card {
        padding: 1.5rem !important;
        margin-bottom: 1rem !important;
    }

    .service-card h3 {
        font-size: 1.2rem !important;
        line-height: 1.3 !important;
        margin-bottom: 0.8rem !important;
    }

    .service-card p {
        font-size: 0.95rem !important;
        line-height: 1.5 !important;
    }

    /* Fix contact form on mobile */
    .contact-form .form-group {
        margin-bottom: 1.2rem !important;
    }

    .contact-form input,
    .contact-form select,
    .contact-form textarea {
        padding: 0.8rem !important;
        font-size: 1rem !important;
        min-height: 48px !important;
    }

    /* Improve footer on mobile */
    .footer-content {
        gap: 2rem !important;
    }

    .footer-section h3,
    .footer-section h4 {
        font-size: 1.1rem !important;
        margin-bottom: 1rem !important;
    }

    /* Fix quick actions positioning */
    .quick-actions {
        bottom: 20px !important;
        right: 15px !important;
        gap: 10px !important;
    }

    .quick-btn {
        width: 50px !important;
        height: 50px !important;
        font-size: 1.2rem !important;
    }

    /* Fix WhatsApp float button */
    .whatsapp-float {
        bottom: 90px !important;
        right: 15px !important;
        width: 55px !important;
        height: 55px !important;
        font-size: 1.5rem !important;
    }

    /* Fix get started button */
    .get-started-fixed {
        bottom: 15px !important;
        left: 15px !important;
        right: 15px !important;
    }

    .get-started-fixed .btn {
        width: 100% !important;
        padding: 1rem !important;
        font-size: 1rem !important;
        min-height: 50px !important;
    }
}
