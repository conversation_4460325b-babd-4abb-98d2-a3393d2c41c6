<!DOCTYPE html>
<html lang="mr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>धन्यवाद - रिया सॉफ्टवेअर</title>
    <meta name="description" content="तुमचा संदेश यशस्वीरित्या पाठवला गेला आहे. आम्ही लवकरच तुमच्याशी संपर्क साधू.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="images/favicon.svg">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    
    <!-- Google Fonts for Marathi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&family=Hind:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <link rel="stylesheet" href="styles.css">
    
    <style>
        .thank-you-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--white);
            text-align: center;
            padding: 2rem;
        }
        
        .thank-you-content {
            max-width: 600px;
            padding: 3rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-lg);
        }
        
        .thank-you-icon {
            font-size: 4rem;
            color: var(--white);
            margin-bottom: 2rem;
            animation: bounce 2s infinite;
        }
        
        .thank-you-content h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .thank-you-content p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .thank-you-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-white {
            background: var(--white);
            color: var(--primary-color);
            border: 2px solid var(--white);
        }
        
        .btn-white:hover {
            background: transparent;
            color: var(--white);
        }
        
        .btn-outline-white {
            background: transparent;
            color: var(--white);
            border: 2px solid var(--white);
        }
        
        .btn-outline-white:hover {
            background: var(--white);
            color: var(--primary-color);
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }
        
        @media (max-width: 768px) {
            .thank-you-content {
                padding: 2rem;
                margin: 1rem;
            }
            
            .thank-you-content h1 {
                font-size: 2rem;
            }
            
            .thank-you-content p {
                font-size: 1.1rem;
            }
            
            .thank-you-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <section class="thank-you-section">
        <div class="thank-you-content">
            <div class="thank-you-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h1>धन्यवाद!</h1>
            <p>तुमचा संदेश यशस्वीरित्या पाठवला गेला आहे. आमची टीम लवकरच तुमच्याशी संपर्क साधेल आणि तुमच्या प्रकल्पाबद्दल चर्चा करेल.</p>
            <p>आम्ही 24 तासांच्या आत तुम्हाला उत्तर देण्याचा प्रयत्न करतो.</p>
            
            <div class="thank-you-buttons">
                <a href="index.html" class="btn btn-white">
                    <i class="fas fa-home"></i> होमपेजवर जा
                </a>
                <a href="https://wa.me/919604069989?text=नमस्कार! मी नुकताच फॉर्म भरला आहे." class="btn btn-outline-white" target="_blank">
                    <i class="fab fa-whatsapp"></i> व्हाट्सॲप करा
                </a>
            </div>
        </div>
    </section>

    <!-- Auto redirect after 10 seconds -->
    <script>
        // Show countdown
        let countdown = 10;
        const countdownElement = document.createElement('p');
        countdownElement.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 0.9rem;
            z-index: 1000;
        `;
        document.body.appendChild(countdownElement);
        
        const updateCountdown = () => {
            countdownElement.textContent = `${countdown} सेकंदात होमपेजवर जाणार...`;
            countdown--;
            
            if (countdown < 0) {
                window.location.href = 'index.html';
            }
        };
        
        updateCountdown();
        const interval = setInterval(updateCountdown, 1000);
        
        // Clear interval if user navigates away
        window.addEventListener('beforeunload', () => {
            clearInterval(interval);
        });
    </script>
</body>
</html>
