/* Zyka POS Specific Styles */

/* POS Hero Section */
.pos-hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #06b6d4 100%);
    color: var(--white);
    padding-top: 80px;
    position: relative;
    overflow: hidden;
}

.pos-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.pos-logo {
    text-align: center;
    margin-bottom: 2rem;
}

.pos-logo h1 {
    font-size: 4rem;
    font-weight: 800;
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.pos-logo .tagline {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.pos-hero .hero-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.pos-hero .hero-content p {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    line-height: 1.6;
}

/* POS Mockup */
.pos-mockup {
    position: relative;
    max-width: 400px;
    margin: 0 auto;
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: transform 0.3s ease;
}

.pos-mockup:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg) scale(1.02);
}

.screen {
    background: #1f2937;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 3px solid #374151;
    position: relative;
    overflow: hidden;
}

.screen::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.pos-interface {
    background: #111827;
    border-radius: 12px;
    padding: 15px;
    color: white;
    font-family: 'Courier New', monospace;
}

.header-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #374151;
    margin-bottom: 15px;
}

.logo-small {
    color: #fbbf24;
    font-weight: bold;
    font-size: 14px;
}

.time {
    color: #9ca3af;
    font-size: 12px;
}

.menu-items {
    margin-bottom: 15px;
}

.item {
    padding: 8px 0;
    border-bottom: 1px solid #374151;
    font-size: 14px;
    color: #e5e7eb;
}

.total {
    text-align: right;
    font-weight: bold;
    color: #10b981;
    font-size: 16px;
    padding-top: 10px;
    border-top: 2px solid #374151;
}

/* Features Section */
.pos-features {
    padding: 6rem 0;
    background: var(--light-bg);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(350px, 100%), 1fr));
    gap: 2.5rem;
}

.feature-card {
    background: var(--white);
    padding: 3rem 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1e3a8a, #3b82f6, #06b6d4);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
    border-color: #3b82f6;
}

.feature-icon {
    width: 90px;
    height: 90px;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2.2rem;
    color: var(--white);
    position: relative;
}

.feature-icon::after {
    content: '';
    position: absolute;
    inset: -3px;
    background: linear-gradient(45deg, #1e3a8a, #3b82f6, #06b6d4);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-card:hover .feature-icon::after {
    opacity: 1;
}

.feature-card h3 {
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.feature-card p {
    color: var(--text-light);
    line-height: 1.7;
    font-size: 1.1rem;
}

/* Pricing Section */
.pos-pricing {
    padding: 6rem 0;
    background: var(--white);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(320px, 100%), 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 2.5rem 2rem;
    text-align: center;
    transition: var(--transition);
    border: 2px solid transparent;
    position: relative;
}

.pricing-card.featured {
    border-color: #3b82f6;
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.plan-header h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.price {
    margin-bottom: 2rem;
}

.currency {
    font-size: 1.5rem;
    color: var(--text-light);
    vertical-align: top;
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: #3b82f6;
}

.period {
    font-size: 1rem;
    color: var(--text-light);
    margin-left: 0.5rem;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
}

.plan-features li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.plan-features li:last-child {
    border-bottom: none;
}

.plan-features i {
    color: #10b981;
    font-size: 1.1rem;
}

.btn-outline {
    background: transparent;
    color: #3b82f6;
    border: 2px solid #3b82f6;
}

.btn-outline:hover {
    background: #3b82f6;
    color: white;
}

/* Demo Section */
.pos-demo {
    padding: 6rem 0;
    background: var(--light-bg);
}

.demo-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.demo-video {
    position: relative;
}

.video-placeholder {
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    border-radius: var(--border-radius);
    padding: 4rem 2rem;
    text-align: center;
    color: white;
    cursor: pointer;
    transition: var(--transition);
}

.video-placeholder:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

.video-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.video-placeholder p {
    font-size: 1.3rem;
    font-weight: 600;
}

.demo-info h3 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.demo-info p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.demo-benefits {
    list-style: none;
    padding: 0;
    margin-bottom: 2.5rem;
}

.demo-benefits li {
    padding: 0.75rem 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-dark);
}

.demo-benefits i {
    color: #10b981;
    font-size: 1.1rem;
}

/* CTA Section */
.pos-cta {
    padding: 6rem 0;
    background: linear-gradient(135deg, #1e3a8a, #3b82f6);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.cta-content p {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-large {
    padding: 1.2rem 2.5rem;
    font-size: 1.2rem;
    font-weight: 600;
}

/* Active Navigation */
.nav-menu a.active {
    color: var(--primary-color);
    font-weight: 600;
}

.nav-menu a.active::after {
    width: 100%;
}

/* Mobile Navigation Enhancements */
@media (max-width: 767px) {
    .nav-menu {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
    }

    .nav-menu a {
        padding: 1.2rem;
        font-size: 1.1rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .nav-menu a:last-child {
        border-bottom: none;
    }

    .nav-menu a.active {
        background: rgba(255, 107, 53, 0.1);
        color: var(--primary-color);
    }

    .hamburger span {
        background: var(--text-dark);
        transition: all 0.3s ease;
    }

    .hamburger.active span:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
        background: var(--primary-color);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
        background: var(--primary-color);
    }
}

/* Responsive Design for POS Page */

/* Large Desktop Screens (1440px and above) */
@media (min-width: 1440px) {
    .pos-logo h1 {
        font-size: 5rem;
    }

    .pos-hero .hero-content h2 {
        font-size: 3rem;
    }

    .pos-hero .hero-content p {
        font-size: 1.4rem;
    }

    .feature-card {
        padding: 3.5rem 2.5rem;
    }

    .feature-icon {
        width: 110px;
        height: 110px;
        font-size: 2.5rem;
    }

    .pricing-card {
        padding: 3rem 2.5rem;
    }

    .amount {
        font-size: 3.5rem;
    }
}

/* Desktop and Large Tablets (1024px to 1439px) */
@media (min-width: 1024px) and (max-width: 1439px) {
    .pos-logo h1 {
        font-size: 3.5rem;
    }

    .pos-hero .hero-content h2 {
        font-size: 2.2rem;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .pricing-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
}

/* Tablets (768px to 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .pos-hero {
        padding-top: 100px;
    }

    .pos-logo h1 {
        font-size: 3rem;
    }

    .pos-hero .hero-content h2 {
        font-size: 2rem;
    }

    .pos-hero .hero-content p {
        font-size: 1.2rem;
    }

    .hero-buttons {
        flex-direction: row;
        gap: 1rem;
    }

    .hero-buttons .btn {
        flex: 1;
        max-width: 200px;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .pricing-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .pricing-card.featured {
        grid-column: span 2;
        max-width: 400px;
        margin: 0 auto;
    }

    .demo-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .cta-buttons {
        flex-direction: row;
        gap: 1.5rem;
    }
}

/* Mobile Devices (up to 767px) */
@media (max-width: 767px) {
    .pos-hero {
        padding-top: 80px;
        min-height: 90vh;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
        padding: 2rem 20px;
    }

    .pos-logo h1 {
        font-size: 2.5rem;
    }

    .pos-logo .tagline {
        font-size: 1.1rem;
    }

    .pos-hero .hero-content h2 {
        font-size: 1.8rem;
        line-height: 1.3;
    }

    .pos-hero .hero-content p {
        font-size: 1.1rem;
        line-height: 1.5;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 280px;
    }

    .pos-mockup {
        max-width: 300px;
    }

    .screen {
        padding: 15px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .feature-card {
        padding: 2rem 1.5rem;
    }

    .feature-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .pricing-card.featured {
        transform: none;
        order: -1;
    }

    .demo-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .video-placeholder {
        padding: 3rem 1.5rem;
    }

    .video-placeholder i {
        font-size: 3rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .btn-large {
        width: 100%;
        max-width: 300px;
    }

    .cta-content h2 {
        font-size: 2rem;
        line-height: 1.2;
    }

    .cta-content p {
        font-size: 1.1rem;
    }

    .faq-container {
        margin: 0 -10px;
    }

    .faq-item {
        margin: 0 10px 1rem 10px;
    }

    .faq-question {
        padding: 1.2rem;
    }

    .faq-question h3 {
        font-size: 1.1rem;
        line-height: 1.3;
    }
}

/* Large Mobile Devices (481px to 767px) */
@media (min-width: 481px) and (max-width: 767px) {
    .pos-logo h1 {
        font-size: 2.8rem;
    }

    .pos-hero .hero-content h2 {
        font-size: 2rem;
    }

    .hero-buttons {
        flex-direction: row;
        justify-content: center;
    }

    .hero-buttons .btn {
        flex: 1;
        max-width: 200px;
    }

    .feature-card {
        padding: 2.5rem 2rem;
    }

    .pricing-card {
        padding: 2.5rem 2rem;
    }
}

/* Standard Mobile Devices (376px to 480px) */
@media (min-width: 376px) and (max-width: 480px) {
    .pos-logo h1 {
        font-size: 2.2rem;
    }

    .pos-logo .tagline {
        font-size: 1rem;
    }

    .pos-hero .hero-content h2 {
        font-size: 1.6rem;
    }

    .pos-hero .hero-content p {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 250px;
    }

    .feature-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }

    .feature-card h3 {
        font-size: 1.4rem;
    }

    .pricing-card {
        padding: 2rem 1.5rem;
    }

    .amount {
        font-size: 2.5rem;
    }

    .cta-content h2 {
        font-size: 1.8rem;
    }

    .video-placeholder {
        padding: 2.5rem 1rem;
    }
}

/* Small Mobile Devices (321px to 375px) */
@media (min-width: 321px) and (max-width: 375px) {
    .pos-logo h1 {
        font-size: 2rem;
    }

    .pos-logo .tagline {
        font-size: 0.9rem;
    }

    .pos-hero .hero-content h2 {
        font-size: 1.4rem;
    }

    .pos-hero .hero-content p {
        font-size: 0.95rem;
    }

    .hero-buttons .btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.95rem;
    }

    .feature-card {
        padding: 1.5rem 1.2rem;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .feature-card h3 {
        font-size: 1.2rem;
    }

    .pricing-card {
        padding: 1.5rem 1.2rem;
    }

    .amount {
        font-size: 2.2rem;
    }

    .btn-large {
        padding: 1rem 2rem;
        font-size: 1rem;
    }
}

/* Extra Small Devices (up to 320px) */
@media (max-width: 320px) {
    .pos-hero {
        padding-top: 70px;
    }

    .hero-container {
        padding: 1.5rem 15px;
        gap: 1.5rem;
    }

    .pos-logo h1 {
        font-size: 1.8rem;
    }

    .pos-logo .tagline {
        font-size: 0.8rem;
    }

    .pos-hero .hero-content h2 {
        font-size: 1.2rem;
        line-height: 1.2;
    }

    .pos-hero .hero-content p {
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .hero-buttons .btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.9rem;
        max-width: 220px;
    }

    .pos-mockup {
        max-width: 250px;
    }

    .screen {
        padding: 10px;
    }

    .feature-card {
        padding: 1.2rem 1rem;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .feature-card h3 {
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .feature-card p {
        font-size: 0.9rem;
    }

    .pricing-card {
        padding: 1.2rem 1rem;
    }

    .amount {
        font-size: 2rem;
    }

    .plan-features li {
        font-size: 0.9rem;
        padding: 0.5rem 0;
    }

    .cta-content h2 {
        font-size: 1.5rem;
        line-height: 1.2;
    }

    .cta-content p {
        font-size: 1rem;
    }

    .btn-large {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
        max-width: 200px;
    }

    .video-placeholder {
        padding: 2rem 1rem;
    }

    .video-placeholder i {
        font-size: 2.5rem;
    }

    .demo-info h3 {
        font-size: 1.5rem;
    }

    .demo-benefits li {
        font-size: 0.9rem;
    }

    .faq-question {
        padding: 1rem;
    }

    .faq-question h3 {
        font-size: 1rem;
    }

    .faq-answer {
        padding: 1rem;
    }

    .faq-answer p {
        font-size: 0.9rem;
    }
}

/* Landscape Orientation for Mobile */
@media (max-width: 767px) and (orientation: landscape) {
    .pos-hero {
        min-height: 100vh;
        padding-top: 60px;
    }

    .hero-container {
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
        align-items: center;
    }

    .pos-logo h1 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .pos-hero .hero-content h2 {
        font-size: 1.4rem;
        margin-bottom: 1rem;
    }

    .pos-hero .hero-content p {
        font-size: 0.95rem;
        margin-bottom: 1.5rem;
    }

    .hero-buttons {
        flex-direction: row;
        gap: 0.8rem;
    }

    .hero-buttons .btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }

    .pos-features,
    .pos-pricing,
    .pos-demo,
    .pos-cta {
        padding: 3rem 0;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .feature-card:hover,
    .pricing-card:hover,
    .video-placeholder:hover,
    .pos-mockup:hover {
        transform: none;
    }

    .feature-card:active,
    .pricing-card:active {
        transform: scale(0.98);
    }

    .btn:active {
        transform: scale(0.95);
    }

    .hero-buttons .btn,
    .btn-large {
        min-height: 44px;
        min-width: 44px;
    }

    .pos-mockup {
        transform: none;
    }

    .faq-question {
        min-height: 44px;
        display: flex;
        align-items: center;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .pos-mockup .screen {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles */
@media print {
    .header,
    .whatsapp-float,
    .hero-buttons,
    .cta-buttons {
        display: none !important;
    }

    .pos-hero {
        min-height: auto;
        padding: 2rem 0;
        background: white !important;
        color: black !important;
    }

    .pos-logo h1 {
        color: black !important;
    }

    .section {
        page-break-inside: avoid;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .pos-mockup,
    .feature-card,
    .pricing-card,
    .video-placeholder {
        transition: none !important;
        transform: none !important;
    }

    .pos-mockup:hover {
        transform: none !important;
    }
}

/* Focus Styles for Accessibility */
.btn:focus,
.faq-question:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Container Improvements */
@media (max-width: 1200px) {
    .container {
        padding: 0 30px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 20px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
}

@media (max-width: 320px) {
    .container {
        padding: 0 10px;
    }
}
